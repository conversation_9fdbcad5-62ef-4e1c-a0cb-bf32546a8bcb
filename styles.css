/* Reset dan Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Montserrat', sans-serif;
    line-height: 1.6;
    color: #4A3933;
    background-color: #F4F1EA;
    font-size: 16px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #4A3933;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    text-align: center;
}

.btn-primary {
    background-color: #E86A33;
    color: white;
}

.btn-primary:hover {
    background-color: #d55a28;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(232, 106, 51, 0.3);
}

.btn-whatsapp {
    background-color: #25D366;
    color: white;
    font-size: 18px;
    padding: 18px 35px;
}

.btn-whatsapp:hover {
    background-color: #20b954;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 211, 102, 0.4);
}

.btn-large {
    font-size: 20px;
    padding: 20px 40px;
}

/* Navbar */
.navbar {
    background-color: rgba(244, 241, 234, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(74, 57, 51, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    font-family: 'Playfair Display', serif;
    color: #4A3933;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #4A3933;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #E86A33;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #4A3933;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(135deg, rgba(244, 241, 234, 0.9), rgba(232, 106, 51, 0.1)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="coffee" patternUnits="userSpaceOnUse" width="20" height="20"><circle cx="10" cy="10" r="1" fill="%23E86A33" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23coffee)"/></svg>');
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.hero-container {
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: #36302d;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    color: #3d3434;
    opacity: 0.8;
}

/* About Section */
.about {
    padding: 100px 0;
    background-color: #F4F1EA;
}

.about-content {
    text-align: center;
}

.about-text {
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto 4rem;
    line-height: 1.8;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.feature {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(74, 57, 51, 0.1);
    transition: transform 0.3s ease;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #E86A33;
}

/* Menu Section */
.menu {
    padding: 100px 0;
    background: linear-gradient(45deg, #F4F1EA 0%, rgba(232, 106, 51, 0.05) 100%);
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
    margin-bottom: 4rem;
}

.menu-item {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 8px 25px rgba(74, 57, 51, 0.1);
    transition: all 0.5s ease;
}

.menu-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(74, 57, 51, 0.15);
}

.menu-image {
    margin-bottom: 1.5rem;
}

.placeholder-image {
    width: 80px;
    height: 80px;
    background-color: #E86A33;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto;
    color: white;
}

.menu-item h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #4A3933;
}

.menu-item p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.price {
    font-size: 1.3rem;
    font-weight: 600;
    color: #E86A33;
}

.menu-cta {
    text-align: center;
    margin-top: 3rem;
}

/* Location Section */
.location {
    padding: 100px 0;
    background-color: #F4F1EA;
}

.location-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.location-info h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #E86A33;
    margin-top: 2rem;
}

.location-info h3:first-child {
    margin-top: 0;
}

.location-info p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.map-placeholder {
    background: white;
    border-radius: 15px;
    padding: 4rem 2rem;
    text-align: center;
    box-shadow: 0 8px 25px rgba(74, 57, 51, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.map-placeholder:hover {
    transform: translateY(-5px);
}

.map-placeholder p {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* CTA Section */
.cta {
    padding: 100px 0;
    background: linear-gradient(135deg, #E86A33, #d55a28);
    color: white;
    text-align: center;
}

.cta h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.cta p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
}

/* Footer */
.footer {
    background-color: #4A3933;
    color: #F4F1EA;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #E86A33;
}

.footer-section p {
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(244, 241, 234, 0.2);
    opacity: 0.7;
}

/* Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Mobile Styles */
@media screen and (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(244, 241, 234, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(10px);
        padding: 2rem 0;
        gap: 1rem;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .location-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-features {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .menu-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .btn {
        padding: 12px 25px;
        font-size: 14px;
    }

    .btn-whatsapp {
        font-size: 16px;
        padding: 15px 30px;
    }

    .btn-large {
        font-size: 18px;
        padding: 18px 35px;
    }
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

/* Performance Optimizations */
.hero, .about, .menu, .location, .cta {
    will-change: transform;
}

.btn, .menu-item, .feature {
    will-change: transform;
}

/* Print Styles */
@media print {
    .navbar, .btn-whatsapp, .nav-toggle, .mobile-overlay {
        display: none !important;
    }

    .hero {
        height: auto;
        padding: 2rem 0;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .section-title {
        font-size: 18pt;
        margin-bottom: 1rem;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn-primary {
        background-color: #000;
        color: #fff;
        border: 2px solid #fff;
    }

    .btn-whatsapp {
        background-color: #000;
        color: #fff;
        border: 2px solid #fff;
    }

    .navbar {
        background-color: #fff;
        border-bottom: 2px solid #000;
    }

    .nav-link {
        color: #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .fade-in {
        opacity: 1;
        transform: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #2d2520;
        --text-color: #f4f1ea;
        --accent-color: #ff7a47;
    }

    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }

    .navbar {
        background-color: rgba(45, 37, 32, 0.95);
    }

    .menu-item, .feature {
        background-color: #3a2f28;
        color: var(--text-color);
    }

    .footer {
        background-color: #1a1410;
    }
}
