# 🚀 Panduan Deployment - Kopi Senja

Panduan lengkap untuk deploy website Kopi Senja ke berbagai platform hosting.

## 📋 Persiapan Sebelum Deploy

### 1. Checklist Pre-Deployment
- [ ] Test website di localhost
- [ ] Verifikasi responsivitas di berbagai device
- [ ] Test semua link WhatsApp
- [ ] Optimasi gambar (jika ada)
- [ ] Minify CSS dan JavaScript (opsional)
- [ ] Update nomor WhatsApp yang benar
- [ ] Update informasi kontak dan alamat

### 2. File yang <PERSON>an
```
kopi-senja/
├── index.html          # Halaman utama
├── styles.css          # Stylesheet utama
├── script.js           # JavaScript interaktif
├── manifest.json       # PWA manifest
├── README.md           # Dokumentasi
├── DEPLOYMENT.md       # Panduan deployment
└── test.html          # Halaman testing (opsional)
```

## 🌐 Platform Hosting Gratis

### 1. Netlify (Recommended)

**Kelebihan:**
- Deploy otomatis dari Git
- HTTPS gratis
- Custom domain gratis
- CDN global
- Form handling

**Cara Deploy:**
1. Buat akun di [netlify.com](https://netlify.com)
2. Connect ke GitHub repository
3. Set build settings:
   - Build command: (kosong)
   - Publish directory: `/`
4. Deploy!

**Custom Domain:**
1. Beli domain di provider (Niagahoster, Dewaweb, dll)
2. Di Netlify: Site settings > Domain management
3. Add custom domain
4. Update DNS records di provider domain

### 2. Vercel

**Cara Deploy:**
1. Install Vercel CLI: `npm i -g vercel`
2. Di folder project: `vercel`
3. Follow setup wizard
4. Deploy: `vercel --prod`

### 3. GitHub Pages

**Cara Deploy:**
1. Push code ke GitHub repository
2. Settings > Pages
3. Source: Deploy from branch
4. Branch: main/master
5. Folder: / (root)

### 4. Firebase Hosting

**Cara Deploy:**
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login: `firebase login`
3. Init: `firebase init hosting`
4. Deploy: `firebase deploy`

## 🔧 Konfigurasi Domain

### Update Nomor WhatsApp
Ganti semua instance nomor WhatsApp di `index.html`:
```html
<!-- Ganti nomor ini -->
href="https://wa.me/6281234567890?text=..."

<!-- Dengan nomor yang benar -->
href="https://wa.me/628XXXXXXXXXX?text=..."
```

### Update Informasi Kontak
Edit bagian location dan footer di `index.html`:
```html
<!-- Update alamat -->
<p>Jl. Alamat Sebenarnya No. XX<br>Kota, Provinsi XXXXX</p>

<!-- Update jam buka -->
<p>Senin - Jumat: XX:XX - XX:XX<br>
   Sabtu - Minggu: XX:XX - XX:XX</p>
```

### Update Meta Tags
Edit meta tags di `<head>` untuk SEO:
```html
<meta property="og:url" content="https://domain-anda.com/">
<meta property="og:image" content="https://domain-anda.com/og-image.jpg">
```

## 📊 Optimasi Performance

### 1. Minify CSS
Gunakan tools online atau:
```bash
# Menggunakan cssnano
npm install -g cssnano-cli
cssnano styles.css styles.min.css
```

### 2. Minify JavaScript
```bash
# Menggunakan terser
npm install -g terser
terser script.js -o script.min.js
```

### 3. Optimasi Gambar
- Gunakan format WebP untuk gambar
- Compress gambar dengan TinyPNG
- Gunakan lazy loading untuk gambar

### 4. Enable Gzip
Tambahkan file `.htaccess` (untuk Apache):
```apache
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

## 🔒 Security Headers

Tambahkan security headers di hosting:

### Netlify (_headers file)
```
/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Content-Security-Policy: default-src 'self'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src fonts.gstatic.com; script-src 'self' 'unsafe-inline'
```

## 📈 Analytics & Monitoring

### 1. Google Analytics
Tambahkan di `<head>`:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### 2. Google Search Console
1. Verify ownership website
2. Submit sitemap.xml
3. Monitor search performance

### 3. PageSpeed Insights
Test performance di [pagespeed.web.dev](https://pagespeed.web.dev)

## 🎯 SEO Checklist

- [ ] Title tag unik dan deskriptif
- [ ] Meta description menarik
- [ ] URL structure yang clean
- [ ] Internal linking yang baik
- [ ] Schema markup (sudah ada)
- [ ] Sitemap.xml
- [ ] Robots.txt
- [ ] Open Graph tags
- [ ] Twitter Cards

## 📱 PWA Setup

File `manifest.json` sudah disediakan. Untuk PWA penuh:

1. **Service Worker** (opsional):
```javascript
// sw.js
self.addEventListener('install', (e) => {
  e.waitUntil(
    caches.open('kopi-senja-v1').then((cache) => {
      return cache.addAll([
        '/',
        '/styles.css',
        '/script.js',
        '/manifest.json'
      ]);
    })
  );
});
```

2. **Register Service Worker**:
```javascript
// Di script.js
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js');
}
```

## 🐛 Troubleshooting

### Website Tidak Muncul
1. Check file path dan nama file
2. Pastikan index.html di root directory
3. Check console browser untuk error

### CSS Tidak Load
1. Check path CSS di HTML
2. Pastikan file CSS ada
3. Check MIME type di server

### JavaScript Error
1. Open browser console
2. Check syntax error
3. Pastikan semua dependencies ada

### WhatsApp Link Tidak Berfungsi
1. Check format nomor (62XXXXXXXXXX)
2. Test di browser mobile
3. Pastikan URL encoding benar

## 📞 Support

Jika ada masalah deployment, check:
1. Browser console untuk error
2. Network tab untuk failed requests
3. Hosting provider documentation
4. Community forums

---

**Happy Deploying! ☕** 

Semoga website Kopi Senja sukses dan banyak yang pesan via WhatsApp! 🚀
