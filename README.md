# Kopi <PERSON>ja - Landing Page

Landing page untuk kedai kopi fiktif "Kopi Senja" dengan desain modern-rustic yang hangat dan nyaman.

## 🎯 Tujuan

Website ini dibuat untuk:
- Branding kedai kopi "Kopi Senja"
- Mengarahkan pemesanan ke WhatsApp
- Menampilkan menu unggulan dan informasi kedai

## ✨ Fitur Utama

### 🎨 Desain
- **Tema**: Modern-rustic dengan nuansa hangat dan cozy
- **Skema <PERSON>**: 
  - Background: `#F4F1EA` (krem hangat)
  - Teks: `#4A3933` (coklat gelap)
  - Aksen: `#E86A33` (oranye hangat)
  - WhatsApp: `#25D366` (hijau WhatsApp)
- **Typography**: 
  - Judul: Playfair Display (serif elegan)
  - Body: Montserrat (sans-serif modern)

### 📱 Responsif
- **Flexbox/Grid**: Layout yang fleksibel untuk semua ukuran layar
- **Mobile-First**: Optimized untuk pengalaman mobile
- **Breakpoint**: 768px untuk transisi desktop/mobile

### 🎭 Animasi & Interaksi
- **Scroll Animations**: Fade-in effect pada setiap section
- **Smooth Transitions**: Transisi halus pada semua elemen interaktif
- **Hover Effects**: Efek hover yang konsisten pada tombol dan kartu
- **Parallax**: Efek parallax ringan pada hero section

### 🍔 Navigation
- **Sticky Navbar**: Navbar tetap di atas saat scroll
- **Mobile Menu**: Hamburger menu dengan overlay gelap
- **Smooth Scroll**: Navigasi halus antar section

### 📞 Call-to-Action
- **WhatsApp Integration**: Tombol WhatsApp yang menonjol
- **Multiple CTAs**: Beberapa titik konversi ke WhatsApp
- **Prominent Buttons**: Tombol hijau WhatsApp yang kontras

## 📋 Struktur Konten

1. **Navbar** - Navigasi sticky dengan logo dan menu
2. **Hero Section** - Headline utama dengan CTA
3. **About Us** - Cerita dan nilai kedai kopi
4. **Menu Section** - Menu unggulan dengan harga
5. **Location Section** - Alamat, jam buka, dan kontak
6. **CTA Section** - Call-to-action utama
7. **Footer** - Informasi kontak dan jam buka

## 🚀 Cara Menjalankan

1. **Clone atau download** file project
2. **Buka `index.html`** di browser
3. **Atau gunakan live server** untuk development

### Menggunakan Live Server (Recommended)

```bash
# Jika menggunakan VS Code dengan Live Server extension
# Klik kanan pada index.html > "Open with Live Server"

# Atau menggunakan Python
python -m http.server 8000

# Atau menggunakan Node.js
npx serve .
```

## 📱 Fitur Mobile

- **Hamburger Menu**: Menu mobile dengan animasi smooth
- **Touch Optimized**: Ukuran tombol yang nyaman untuk touch
- **Mobile Overlay**: Background gelap saat menu terbuka
- **Responsive Images**: Placeholder yang menyesuaikan ukuran layar

## 🎯 WhatsApp Integration

Tombol WhatsApp sudah dikonfigurasi dengan:
- **Nomor**: +62 812-3456-7890 (ganti sesuai kebutuhan)
- **Pesan Default**: Template pesan yang sudah disiapkan
- **Multiple Entry Points**: Beberapa tombol di section berbeda

### Cara Mengubah Nomor WhatsApp

Ganti nomor di file `index.html` pada bagian:
```html
href="https://wa.me/6281234567890?text=..."
```

## 🎨 Customization

### Mengubah Warna
Edit variabel warna di `styles.css`:
```css
/* Warna utama */
background-color: #F4F1EA; /* Background */
color: #4A3933; /* Teks */
background-color: #E86A33; /* Aksen */
background-color: #25D366; /* WhatsApp */
```

### Mengubah Font
Ganti import font di `index.html`:
```html
<link href="https://fonts.googleapis.com/css2?family=..." rel="stylesheet">
```

### Menambah Menu Item
Duplikasi struktur `.menu-item` di section menu:
```html
<div class="menu-item">
    <div class="menu-image">
        <div class="placeholder-image">☕</div>
    </div>
    <h3>Nama Menu</h3>
    <p>Deskripsi menu...</p>
    <span class="price">Rp XX.000</span>
</div>
```

## 🔧 Browser Support

- ✅ Chrome 60+
- ✅ Firefox 60+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers

## 📊 Performance

- **Lightweight**: Tidak menggunakan framework berat
- **Fast Loading**: Optimized CSS dan JavaScript
- **SEO Friendly**: Struktur HTML semantik
- **Accessible**: Support keyboard navigation

## 🛠️ Technologies Used

- **HTML5**: Struktur semantik
- **CSS3**: Flexbox, Grid, Animations
- **Vanilla JavaScript**: Interaktivitas tanpa dependencies
- **Google Fonts**: Typography yang elegan

## 📝 License

Project ini dibuat untuk keperluan demonstrasi. Silakan gunakan dan modifikasi sesuai kebutuhan.

## 📞 Support

Jika ada pertanyaan atau butuh bantuan customization, silakan hubungi developer.

---

**Kopi Senja** - *Menemani senjamu dengan secangkir cerita* ☕
